import { NextResponse, NextRequest } from 'next/server';
import { readReviewDataFromS3, writeReviewDataToS3 } from '@/lib/s3-utils';
import { requireAuth } from '@/lib/auth-utils';

export async function POST(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request);
  if (authError) {
    return authError;
  }
  try {
    const { type, tenantId, itemId, editedData } = await request.json();

    if (!type || !tenantId || !itemId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (type === 'positive') {
      // Agregar feedback positivo
      const correctData = await readReviewDataFromS3('correct');

      // Verificar si ya existe
      const existingIndex = correctData.findIndex(
        (item: any) => item.tenantId === tenantId && item.id === itemId
      );

      if (existingIndex === -1) {
        correctData.push({
          tenantId,
          id: itemId,
          timestamp: new Date().toISOString(),
        });

        await writeReviewDataToS3('correct', correctData);
      }

      return NextResponse.json({
        success: true,
        message: 'Positive feedback saved',
      });
    } else if (type === 'negative') {
      // Agregar feedback negativo (corrección)
      if (!editedData) {
        return NextResponse.json(
          { error: 'editedData is required for negative feedback' },
          { status: 400 }
        );
      }

      const editedDataArray = await readReviewDataFromS3('edited');

      // Verificar si ya existe y actualizar o agregar
      const existingIndex = editedDataArray.findIndex(
        (item: any) => item.tenantId === tenantId && item.id === itemId
      );

      const feedbackItem = {
        tenantId,
        id: itemId,
        editedData,
        timestamp: new Date().toISOString(),
      };

      if (existingIndex !== -1) {
        editedDataArray[existingIndex] = feedbackItem;
      } else {
        editedDataArray.push(feedbackItem);
      }

      await writeReviewDataToS3('edited', editedDataArray);

      return NextResponse.json({
        success: true,
        message: 'Negative feedback saved',
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid feedback type' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Error saving feedback:', error);
    return NextResponse.json(
      { error: 'Failed to save feedback' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const tenantId = searchParams.get('tenantId');
    const itemId = searchParams.get('itemId');

    if (!type || !tenantId || !itemId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    if (type === 'positive') {
      const correctData = await readReviewDataFromS3('correct');
      const filteredData = correctData.filter(
        (item: any) => !(item.tenantId === tenantId && item.id === itemId)
      );
      await writeReviewDataToS3('correct', filteredData);
    } else if (type === 'negative') {
      const editedData = await readReviewDataFromS3('edited');
      const filteredData = editedData.filter(
        (item: any) => !(item.tenantId === tenantId && item.id === itemId)
      );
      await writeReviewDataToS3('edited', filteredData);
    } else {
      return NextResponse.json(
        { error: 'Invalid feedback type' },
        { status: 400 }
      );
    }

    return NextResponse.json({ success: true, message: 'Feedback removed' });
  } catch (error) {
    console.error('Error removing feedback:', error);
    return NextResponse.json(
      { error: 'Failed to remove feedback' },
      { status: 500 }
    );
  }
}
