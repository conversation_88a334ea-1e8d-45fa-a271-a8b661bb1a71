import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { requireAuth } from '@/lib/auth-utils';

export async function GET(request: NextRequest) {
  // Check authentication
  const authError = await requireAuth(request);
  if (authError) {
    return authError;
  }
  try {
    // Obtener la ruta a la carpeta de tenants
    const tenantsDir = path.join(process.cwd(), 'data', 'tenants');

    try {
      // Leer todos los archivos en la carpeta
      const files = await fs.readdir(tenantsDir);

      // Filtrar solo archivos JSON y extraer los nombres de tenants
      const tenants = files
        .filter(file => file.endsWith('.json'))
        .map(file => {
          const tenantId = file.replace('.json', '');
          // Remover "_export" del nombre si existe y limpiar el nombre
          const cleanName = tenantId
            .replace(/_export$/, '')
            .replace(/_/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase()); // Capitalizar primera letra de cada palabra

          return {
            id: tenantId,
            name: cleanName,
          };
        })
        .sort((a, b) => a.name.localeCompare(b.name)); // Ordenar alfabéticamente

      return NextResponse.json(tenants);
    } catch (dirError) {
      console.error('Error reading tenants directory:', dirError);
      return NextResponse.json(
        { error: 'Tenants directory not found' },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error('Error fetching tenants:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tenants' },
      { status: 500 }
    );
  }
}
